﻿++解决方案 'Human_OpenSource' ‎ (6 个项目，共 6 个)
i:{00000000-0000-0000-0000-000000000000}:Human_OpenSource.sln
++Assembly-CSharp
i:{00000000-0000-0000-0000-000000000000}:Assembly-CSharp
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++引用
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Assets
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:e:\unity\human_opensource\assets\
++Scripts
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:e:\unity\human_opensource\assets\scripts\
++Animation
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\animation\
++Camera
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\camera\
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\camera\
++Commands
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\
++Chat
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\chat\
++Voice
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\voice\
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\voice\
++Enum
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\enum\
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\enum\
++InfoType.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\enum\infotype.cs
++LLM
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\llm\
++LLMLogic.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\logics\llmlogic.cs
++Logics
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\logics\
++Manager
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\manager\
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\manager\
++Models
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\models\
++Rolle
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\rolle\
++UI
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\
++Events
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\events\
++ScrollViewGPTContent.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\scrollviewgptcontent.cs
++ShowContent.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\showcontent.cs
++StopGenerateButton.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\stopgeneratebutton.cs
++Utilitys
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\utilitys\
++Clipboard.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\utilitys\clipboard.cs
++DataSave.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\utilitys\datasave.cs
++VoiceSynthesis
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\voicesynthesis\
++VoiceTTS.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\voicesynthesis\voicetts.cs
++TransparentWindow
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\transparentwindow\
++LLMConfig.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\logics\llmconfig.cs
++DependensyInject
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{00000000-0000-0000-0000-000000000000}:DependensyInject
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Microsoft.CSharp
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Microsoft.Win32.Primitives
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Mono.Cecil
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++mscorlib
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++netstandard
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++nunit.framework
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++PPv2URPConverters
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.AppContext
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Buffers
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Collections
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Collections.Concurrent
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Collections.NonGeneric
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Collections.Specialized
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ComponentModel
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ComponentModel.Annotations
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ComponentModel.Composition
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ComponentModel.EventBasedAsync
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ComponentModel.Primitives
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ComponentModel.TypeConverter
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Console
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Core
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Data
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Data.Common
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Data.DataSetExtensions
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Diagnostics.Contracts
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Diagnostics.Debug
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Diagnostics.FileVersionInfo
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Diagnostics.Process
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Diagnostics.StackTrace
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Diagnostics.TextWriterTraceListener
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Diagnostics.Tools
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Diagnostics.TraceSource
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Drawing
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Drawing.Primitives
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Dynamic.Runtime
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Globalization
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Globalization.Calendars
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Globalization.Extensions
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.Compression
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.Compression.FileSystem
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.Compression.ZipFile
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.FileSystem
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.FileSystem.DriveInfo
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.FileSystem.Primitives
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.FileSystem.Watcher
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.IsolatedStorage
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.MemoryMappedFiles
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.Pipes
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.IO.UnmanagedMemoryStream
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Linq
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Linq.Expressions
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Linq.Parallel
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Linq.Queryable
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Memory
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.Http
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.Http.Rtc
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.NameResolution
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.NetworkInformation
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.Ping
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.Primitives
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.Requests
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.Security
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.Sockets
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.WebHeaderCollection
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.WebSockets
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Net.WebSockets.Client
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Numerics
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Numerics.Vectors
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ObjectModel
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Reflection
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Reflection.DispatchProxy
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Reflection.Emit
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Reflection.Emit.ILGeneration
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Reflection.Emit.Lightweight
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Reflection.Extensions
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Reflection.Primitives
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Resources.Reader
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Resources.ResourceManager
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Resources.Writer
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.CompilerServices.VisualC
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.Extensions
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.Handles
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.InteropServices
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.InteropServices.RuntimeInformation
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.InteropServices.WindowsRuntime
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.Numerics
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.Serialization
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.Serialization.Formatters
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.Serialization.Json
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.Serialization.Primitives
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Runtime.Serialization.Xml
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Security.Claims
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Security.Cryptography.Algorithms
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Security.Cryptography.Csp
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Security.Cryptography.Encoding
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Security.Cryptography.Primitives
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Security.Cryptography.X509Certificates
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Security.Principal
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Security.SecureString
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ServiceModel.Duplex
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ServiceModel.Http
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ServiceModel.NetTcp
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ServiceModel.Primitives
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ServiceModel.Security
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Text.Encoding
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Text.Encoding.Extensions
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Text.RegularExpressions
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Threading
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Threading.Overlapped
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Threading.Tasks
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Threading.Tasks.Extensions
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Threading.Tasks.Parallel
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Threading.Thread
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Threading.ThreadPool
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Threading.Timer
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Transactions
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.ValueTuple
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Xml
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Xml.Linq
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Xml.ReaderWriter
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Xml.XDocument
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Xml.XmlDocument
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Xml.XmlSerializer
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Xml.XPath
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++System.Xml.XPath.XDocument
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++TypeEventSystem
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{00000000-0000-0000-0000-000000000000}:TypeEventSystem
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:e:\unity\human_opensource\assets\scripts\typeeventsystem\
++uLipSync.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{00000000-0000-0000-0000-000000000000}:uLipSync.Editor
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++uLipSync.Runtime
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{00000000-0000-0000-0000-000000000000}:uLipSync.Runtime
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.2D.Sprite.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Android.Gradle
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Android.GradleProject
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Android.Types
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Burst
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Burst.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Collections
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Collections.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Collections.LowLevel.ILSupport
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.EditorCoroutines.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Mathematics
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Mathematics.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.MemoryProfiler
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.MemoryProfiler.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.MemoryProfiler.Editor.MemoryProfilerModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.RenderPipeline.Universal.ShaderLibrary
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.RenderPipelines.Core.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.RenderPipelines.Core.Runtime
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.RenderPipelines.Core.ShaderLibrary
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.RenderPipelines.Universal.Config.Runtime
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.RenderPipelines.Universal.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.RenderPipelines.Universal.Runtime
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.RenderPipelines.Universal.Shaders
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Searcher.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.ShaderGraph.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Timeline
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.Timeline.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Unity.VisualStudio.Editor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.CoreModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.DeviceSimulatorModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.DiagnosticsModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.EditorToolbarModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.GraphViewModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.PresetsUIModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.QuickSearchModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.SceneTemplateModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.SceneViewModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.TextCoreFontEngineModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.TextCoreTextEngineModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.UI
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.UIBuilderModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.UIElementsModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.UIElementsSamplesModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.UnityConnectModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.AccessibilityModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.AIModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.AndroidJNIModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.AnimationModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.ARModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.AssetBundleModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.AudioModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.ClothModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.ClusterInputModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.ClusterRendererModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.ContentLoadModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.CoreModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.CrashReportingModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.DirectorModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.DSPGraphModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.GameCenterModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.GIModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.GridModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.HotReloadModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.ImageConversionModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.IMGUIModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.InputLegacyModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.InputModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.JSONSerializeModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.LocalizationModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.ParticleSystemModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.PerformanceReportingModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.Physics2DModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.PhysicsModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.ProfilerModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.PropertiesModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.ScreenCaptureModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.SharedInternalsModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.SpriteMaskModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.SpriteShapeModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.StreamingModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.SubstanceModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.SubsystemsModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.TerrainModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.TerrainPhysicsModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.TextCoreFontEngineModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.TextCoreTextEngineModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.TextRenderingModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.TilemapModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.TLSModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UI
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UIElementsModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UIModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UmbraModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityAnalyticsCommonModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityAnalyticsModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityConnectModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityCurlModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityTestProtocolModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityWebRequestAssetBundleModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityWebRequestAudioModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityWebRequestModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityWebRequestTextureModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.UnityWebRequestWWWModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.VehiclesModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.VFXModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.VideoModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.VirtualTexturingModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.VRModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.WindModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.XRModule
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++AutoRotation.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\animation\autorotation.cs
++CameraManager.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\camera\cameramanager.cs
++SwitchCameraCommand.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\camera\switchcameracommand.cs
++AddChatContentCommand.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\chat\addchatcontentcommand.cs
++ChatGPTStartTalkCommand.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\chat\chatgptstarttalkcommand.cs
++GenerateFinishCommand.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\chat\generatefinishcommand.cs
++StopGenerateCommand.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\chat\stopgeneratecommand.cs
++SettingVoiceRecognitionCommand.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\voice\settingvoicerecognitioncommand.cs
++VoiceRecognitionResultCommand.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\commands\voice\voicerecognitionresultcommand.cs
++GameApp.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\manager\gameapp.cs
++AudioModel.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\models\audiomodel.cs
++VoiceRecognizerModel.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\models\voicerecognizermodel.cs
++BlendSpapesControl.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\rolle\blendspapescontrol.cs
++RolleControl.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\rolle\rollecontrol.cs
++RolleManager.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\rolle\rollemanager.cs
++ButtonCopyTextClipboard.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\events\buttoncopytextclipboard.cs
++ClickCopyTextClipboard.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\events\clickcopytextclipboard.cs
++ClickEvent.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\events\clickevent.cs
++VoiceTTSIDSetting.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\voice\voicettsidsetting.cs
++VoiceTTSPanel.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\ui\voice\voicettspanel.cs
++TransparentWindow.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\transparentwindow\transparentwindow.cs
++TransparentWindowRendererFeature.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\transparentwindow\transparentwindowrendererfeature.cs
++UnityEditor.Android.Extensions
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.Graphs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.WebGL.Extensions
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEditor.WindowsStandalone.Extensions
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{4b22a106-e02d-5174-debe-a7295802677c}:
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++uLipSync
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\
++Runtime
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\
++Core
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\
++Debug
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\debug\
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\debug\
++Timeline
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\timeline\
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\timeline\
++uLipSync.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\ulipsync.cs
++uLipSync.Runtime.asmdef
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\ulipsync.runtime.asmdef
++uLipSyncAnimator.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\ulipsyncanimator.cs
++uLipSyncAudioSource.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\ulipsyncaudiosource.cs
++uLipSyncBakedDataPlayer.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\ulipsyncbakeddataplayer.cs
++uLipSyncBlendShape.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\ulipsyncblendshape.cs
++uLipSyncCalibrationAudioPlayer.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\ulipsynccalibrationaudioplayer.cs
++uLipSyncMicrophone.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\ulipsyncmicrophone.cs
++uLipSyncTexture.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\ulipsynctexture.cs
++Algorithm.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\algorithm.cs
++AnimationBakableMonoBehaviour.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\animationbakablemonobehaviour.cs
++BakedData.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\bakeddata.cs
++Common.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\common.cs
++LipSyncJob.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\lipsyncjob.cs
++MicUtil.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\micutil.cs
++Profile.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\profile.cs
++TextureCreator.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\texturecreator.cs
++Util.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\util.cs
++WebGL.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\core\webgl.cs
++DebugAudioPlayer.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\debug\debugaudioplayer.cs
++DebugDump.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\debug\debugdump.cs
++uLipSyncBehaviour.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\timeline\ulipsyncbehaviour.cs
++uLipSyncClip.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\timeline\ulipsyncclip.cs
++uLipSyncMixer.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\timeline\ulipsyncmixer.cs
++uLipSyncTimelineEvent.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\timeline\ulipsynctimelineevent.cs
++uLipSyncTrack.cs
i:{0b3cae41-cd62-d007-4321-42a69e33ead5}:e:\unity\human_opensource\assets\ulipsync\runtime\timeline\ulipsynctrack.cs
++UnityEditor.TestRunner
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.NVIDIAModule
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++UnityEngine.TestRunner
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:
++Editor
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:e:\unity\human_opensource\assets\scripts\dependencyinjection\editor\
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:e:\unity\human_opensource\assets\scripts\typeeventsystem\editor\
++AnimationUtil.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\animationutil.cs
++AudioUtil.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\audioutil.cs
++BakedDataEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\bakeddataeditor.cs
++EditorUtil.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\editorutil.cs
++ProfileEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\profileeditor.cs
++uLipSync.Editor.asmdef
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsync.editor.asmdef
++uLipSyncAnimationWindow.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsyncanimationwindow.cs
++uLipSyncAnimatorEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsyncanimatoreditor.cs
++uLipSyncAudioCalibrationPlayerEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsyncaudiocalibrationplayereditor.cs
++uLipSyncBakedDataPlayerEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsyncbakeddataplayereditor.cs
++uLipSyncBakedDataWindow.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsyncbakeddatawindow.cs
++uLipSyncBlendShapeEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsyncblendshapeeditor.cs
++uLipSyncEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsynceditor.cs
++uLipSyncMicrophoneEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsyncmicrophoneeditor.cs
++uLipSyncTextureEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\ulipsynctextureeditor.cs
++DebugAudioPlayerEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\debug\debugaudioplayereditor.cs
++DebugDumpEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\debug\debugdumpeditor.cs
++DebugUtil.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\debug\debugutil.cs
++uLipSyncClipEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\timeline\ulipsyncclipeditor.cs
++uLipSyncTimelineEventEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\timeline\ulipsynctimelineeventeditor.cs
++uLipSyncTimelineSetupHelper.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\timeline\ulipsynctimelinesetuphelper.cs
++uLipSyncTrackEditor.cs
i:{83f1c566-53b7-6f2c-8693-09dba118e12c}:e:\unity\human_opensource\assets\ulipsync\editor\timeline\ulipsynctrackeditor.cs
++Assembly-CSharp-Editor
i:{00000000-0000-0000-0000-000000000000}:Assembly-CSharp-Editor
++DependencyInjection
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:e:\unity\human_opensource\assets\scripts\dependencyinjection\
++CreateSceneDependencyInjectManager.cs
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:e:\unity\human_opensource\assets\scripts\dependencyinjection\editor\createscenedependencyinjectmanager.cs
++CreateScriptableObject.cs
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:e:\unity\human_opensource\assets\scripts\dependencyinjection\editor\createscriptableobject.cs
++CreateTypeEventSystemManager.cs
i:{fd0f4c45-df5b-5d5b-00f3-fdfb491fa8f4}:e:\unity\human_opensource\assets\scripts\typeeventsystem\editor\createtypeeventsystemmanager.cs
++EventSystem
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\eventsystem\
++Interface
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\eventsystem\interface\
++Kernel
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\kernel\
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\eventsystem\kernel\
++TypeEventSystem.asmdef
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\eventsystem\typeeventsystem.asmdef
++TypeEventSystemManager.cs
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\eventsystem\typeeventsystemmanager.cs
++IEventInterface.cs
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\eventsystem\interface\ieventinterface.cs
++IRegisterCommand.cs
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\eventsystem\interface\iregistercommand.cs
++ISendCommand.cs
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\eventsystem\interface\isendcommand.cs
++EventRegister.cs
i:{a3d15f5e-6e27-89bc-bbcb-eed20cecce1b}:e:\unity\human_opensource\assets\scripts\typeeventsystem\eventsystem\kernel\eventregister.cs
++DI
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\
++Attribute
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\attribute\
++Component
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\component\
++ScriptableObject
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\scriptableobject\
++Utility
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\utility\
++DependensyInject.asmdef
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\dependensyinject.asmdef
++DynamicInjectBindAttribute.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\attribute\dynamicinjectbindattribute.cs
++InjectAttribute.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\attribute\injectattribute.cs
++Item
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\component\item\
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\item\
++AutoInjectDIBind.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\component\autoinjectdibind.cs
++DynamicDIInjectBind.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\component\dynamicdiinjectbind.cs
++BindingType.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\enum\bindingtype.cs
++Context
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\context\
++SceneDependencyInjectContextManager.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\manager\scenedependencyinjectcontextmanager.cs
++DIScriptableObject.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\scriptableobject\discriptableobject.cs
++Extension
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\utility\extension\
++DependencyInjectException.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\utility\dependencyinjectexception.cs
++FilteationUtility.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\utility\filteationutility.cs
++AutoBindingItemStruct.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\component\item\autobindingitemstruct.cs
++DependencyInjectContext.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\context\dependencyinjectcontext.cs
++Public
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\public\
++DependencyInjectItem.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\item\dependencyinjectitem.cs
++BindingImplementationInterfaceExtension.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\utility\extension\bindingimplementationinterfaceextension.cs
++DependencyInjectContextExtension.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\utility\extension\dependencyinjectcontextextension.cs
++ObjectExtension.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\utility\extension\objectextension.cs
++TypeExtension.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\utility\extension\typeextension.cs
++BindingTo.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\kernel\bindingto.cs
++IDependencyInjectItem.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\kernel\idependencyinjectitem.cs
++IGetBindingImplementation.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\kernel\igetbindingimplementation.cs
++IRegisterBinding.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\kernel\iregisterbinding.cs
++DIAwakeInterface.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\public\diawakeinterface.cs
++DIStartInterface.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\public\distartinterface.cs
++IDRegisterBindingInterface.cs
i:{4b22a106-e02d-5174-debe-a7295802677c}:e:\unity\human_opensource\assets\scripts\dependencyinjection\di\kernel\interface\public\idregisterbindinginterface.cs
++Assembly-CSharp (已卸载)
++解决方案 'Human_OpenSource' ‎ (5 个项目，共 6 个)
++LLM.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\llm\llm.cs
++Newtonsoft.Json
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\
++Bson
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\bson\
++Converters
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\
++Linq
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\
++Properties
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\properties\
++Resources
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\resources\
++Schema
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\
++Serialization
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\
++Utilities
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\
++ConstructorHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\constructorhandling.cs
++DateFormatHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\dateformathandling.cs
++DateParseHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\dateparsehandling.cs
++DateTimeZoneHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\datetimezonehandling.cs
++DefaultJsonNameTable.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\defaultjsonnametable.cs
++DefaultValueHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\defaultvaluehandling.cs
++FloatFormatHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\floatformathandling.cs
++FloatParseHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\floatparsehandling.cs
++FormatterAssemblyStyle.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\formatterassemblystyle.cs
++Formatting.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\formatting.cs
++IArrayPool.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\iarraypool.cs
++IJsonLineInfo.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\ijsonlineinfo.cs
++JsonArrayAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonarrayattribute.cs
++JsonConstructorAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonconstructorattribute.cs
++JsonContainerAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsoncontainerattribute.cs
++JsonConvert.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonconvert.cs
++JsonConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonconverter.cs
++JsonConverterAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonconverterattribute.cs
++JsonConverterCollection.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonconvertercollection.cs
++JsonDictionaryAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsondictionaryattribute.cs
++JsonException.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonexception.cs
++JsonExtensionDataAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonextensiondataattribute.cs
++JsonIgnoreAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonignoreattribute.cs
++JsonNameTable.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonnametable.cs
++JsonObjectAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonobjectattribute.cs
++JsonPosition.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonposition.cs
++JsonPropertyAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonpropertyattribute.cs
++JsonReader.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonreader.async.cs
++JsonReader.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonreader.cs
++JsonReaderException.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonreaderexception.cs
++JsonRequiredAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonrequiredattribute.cs
++JsonSerializationException.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonserializationexception.cs
++JsonSerializer.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonserializer.cs
++JsonSerializerSettings.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonserializersettings.cs
++JsonTextReader.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsontextreader.async.cs
++JsonTextReader.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsontextreader.cs
++JsonTextWriter.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsontextwriter.async.cs
++JsonTextWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsontextwriter.cs
++JsonToken.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsontoken.cs
++JsonValidatingReader.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonvalidatingreader.cs
++JsonWriter.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonwriter.async.cs
++JsonWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonwriter.cs
++JsonWriterException.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\jsonwriterexception.cs
++MemberSerialization.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\memberserialization.cs
++MetadataPropertyHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\metadatapropertyhandling.cs
++MissingMemberHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\missingmemberhandling.cs
++NullValueHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\nullvaluehandling.cs
++ObjectCreationHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\objectcreationhandling.cs
++PreserveReferencesHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\preservereferenceshandling.cs
++ReferenceLoopHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\referenceloophandling.cs
++Required.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\required.cs
++SerializationBinder.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serializationbinder.cs
++StringEscapeHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\stringescapehandling.cs
++TraceLevel.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\tracelevel.cs
++TypeNameAssemblyFormatHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\typenameassemblyformathandling.cs
++TypeNameHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\typenamehandling.cs
++WriteState.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\writestate.cs
++BsonBinaryType.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\bson\bsonbinarytype.cs
++BsonBinaryWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\bson\bsonbinarywriter.cs
++BsonObjectId.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\bson\bsonobjectid.cs
++BsonReader.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\bson\bsonreader.cs
++BsonToken.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\bson\bsontoken.cs
++BsonType.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\bson\bsontype.cs
++BsonWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\bson\bsonwriter.cs
++BinaryConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\binaryconverter.cs
++BsonObjectIdConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\bsonobjectidconverter.cs
++CustomCreationConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\customcreationconverter.cs
++DataSetConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\datasetconverter.cs
++DataTableConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\datatableconverter.cs
++DateTimeConverterBase.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\datetimeconverterbase.cs
++DiscriminatedUnionConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\discriminatedunionconverter.cs
++EntityKeyMemberConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\entitykeymemberconverter.cs
++ExpandoObjectConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\expandoobjectconverter.cs
++IsoDateTimeConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\isodatetimeconverter.cs
++JavaScriptDateTimeConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\javascriptdatetimeconverter.cs
++KeyValuePairConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\keyvaluepairconverter.cs
++RegexConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\regexconverter.cs
++StringEnumConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\stringenumconverter.cs
++UnixDateTimeConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\unixdatetimeconverter.cs
++VersionConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\versionconverter.cs
++XmlNodeConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\converters\xmlnodeconverter.cs
++JsonPath
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\
++CommentHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\commenthandling.cs
++DuplicatePropertyNameHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\duplicatepropertynamehandling.cs
++Extensions.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\extensions.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\extensions.cs
++IJEnumerable.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\ijenumerable.cs
++JArray.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jarray.async.cs
++JArray.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jarray.cs
++JConstructor.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jconstructor.async.cs
++JConstructor.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jconstructor.cs
++JContainer.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jcontainer.async.cs
++JContainer.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jcontainer.cs
++JEnumerable.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jenumerable.cs
++JObject.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jobject.async.cs
++JObject.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jobject.cs
++JProperty.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jproperty.async.cs
++JProperty.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jproperty.cs
++JPropertyDescriptor.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jpropertydescriptor.cs
++JPropertyKeyedCollection.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jpropertykeyedcollection.cs
++JRaw.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jraw.async.cs
++JRaw.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jraw.cs
++JsonLoadSettings.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonloadsettings.cs
++JsonMergeSettings.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonmergesettings.cs
++JsonSelectSettings.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonselectsettings.cs
++JToken.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jtoken.async.cs
++JToken.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jtoken.cs
++JTokenEqualityComparer.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jtokenequalitycomparer.cs
++JTokenReader.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jtokenreader.cs
++JTokenType.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jtokentype.cs
++JTokenWriter.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jtokenwriter.async.cs
++JTokenWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jtokenwriter.cs
++JValue.Async.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jvalue.async.cs
++JValue.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jvalue.cs
++LineInfoHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\lineinfohandling.cs
++MergeArrayHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\mergearrayhandling.cs
++MergeNullValueHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\mergenullvaluehandling.cs
++AssemblyInfo.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\properties\assemblyinfo.cs
++link.xml
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\resources\link.xml
++JsonSchema.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschema.cs
++JsonSchemaBuilder.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemabuilder.cs
++JsonSchemaConstants.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemaconstants.cs
++JsonSchemaException.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemaexception.cs
++JsonSchemaGenerator.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemagenerator.cs
++JsonSchemaModel.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemamodel.cs
++JsonSchemaModelBuilder.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemamodelbuilder.cs
++JsonSchemaNode.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemanode.cs
++JsonSchemaNodeCollection.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemanodecollection.cs
++JsonSchemaResolver.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemaresolver.cs
++JsonSchemaType.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschematype.cs
++JsonSchemaWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\jsonschemawriter.cs
++UndefinedSchemaIdHandling.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\undefinedschemaidhandling.cs
++ValidationEventArgs.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\validationeventargs.cs
++ValidationEventHandler.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\schema\validationeventhandler.cs
++CachedAttributeGetter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\cachedattributegetter.cs
++CamelCaseNamingStrategy.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\camelcasenamingstrategy.cs
++CamelCasePropertyNamesContractResolver.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\camelcasepropertynamescontractresolver.cs
++DefaultContractResolver.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\defaultcontractresolver.cs
++DefaultNamingStrategy.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\defaultnamingstrategy.cs
++DefaultReferenceResolver.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\defaultreferenceresolver.cs
++DefaultSerializationBinder.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\defaultserializationbinder.cs
++DiagnosticsTraceWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\diagnosticstracewriter.cs
++DynamicValueProvider.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\dynamicvalueprovider.cs
++ErrorContext.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\errorcontext.cs
++ErrorEventArgs.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\erroreventargs.cs
++ExpressionValueProvider.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\expressionvalueprovider.cs
++FormatterConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\formatterconverter.cs
++IAttributeProvider.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\iattributeprovider.cs
++IContractResolver.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\icontractresolver.cs
++IReferenceResolver.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\ireferenceresolver.cs
++ISerializationBinder.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\iserializationbinder.cs
++ITraceWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\itracewriter.cs
++IValueProvider.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\ivalueprovider.cs
++JsonArrayContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonarraycontract.cs
++JsonContainerContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsoncontainercontract.cs
++JsonContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsoncontract.cs
++JsonDictionaryContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsondictionarycontract.cs
++JsonDynamicContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsondynamiccontract.cs
++JsonFormatterConverter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonformatterconverter.cs
++JsonISerializableContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsoniserializablecontract.cs
++JsonLinqContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonlinqcontract.cs
++JsonObjectContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonobjectcontract.cs
++JsonPrimitiveContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonprimitivecontract.cs
++JsonProperty.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonproperty.cs
++JsonPropertyCollection.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonpropertycollection.cs
++JsonSerializerInternalBase.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonserializerinternalbase.cs
++JsonSerializerInternalReader.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonserializerinternalreader.cs
++JsonSerializerInternalWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonserializerinternalwriter.cs
++JsonSerializerProxy.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonserializerproxy.cs
++JsonStringContract.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsonstringcontract.cs
++JsonTypeReflector.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\jsontypereflector.cs
++KebabCaseNamingStrategy.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\kebabcasenamingstrategy.cs
++MemoryTraceWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\memorytracewriter.cs
++NamingStrategy.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\namingstrategy.cs
++ObjectConstructor.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\objectconstructor.cs
++OnErrorAttribute.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\onerrorattribute.cs
++ReflectionAttributeProvider.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\reflectionattributeprovider.cs
++ReflectionValueProvider.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\reflectionvalueprovider.cs
++SerializationBinderAdapter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\serializationbinderadapter.cs
++SnakeCaseNamingStrategy.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\snakecasenamingstrategy.cs
++TraceJsonReader.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\tracejsonreader.cs
++TraceJsonWriter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\serialization\tracejsonwriter.cs
++AotHelper.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\aothelper.cs
++AsyncUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\asyncutils.cs
++Base64Encoder.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\base64encoder.cs
++BidirectionalDictionary.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\bidirectionaldictionary.cs
++CollectionUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\collectionutils.cs
++CollectionWrapper.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\collectionwrapper.cs
++ConvertUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\convertutils.cs
++DateTimeParser.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\datetimeparser.cs
++DateTimeUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\datetimeutils.cs
++DictionaryWrapper.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\dictionarywrapper.cs
++DynamicProxy.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\dynamicproxy.cs
++DynamicProxyMetaObject.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\dynamicproxymetaobject.cs
++DynamicReflectionDelegateFactory.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\dynamicreflectiondelegatefactory.cs
++DynamicUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\dynamicutils.cs
++EnumInfo.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\enuminfo.cs
++EnumUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\enumutils.cs
++ExpressionReflectionDelegateFactory.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\expressionreflectiondelegatefactory.cs
++FSharpUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\fsharputils.cs
++ILGeneratorExtensions.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\ilgeneratorextensions.cs
++ImmutableCollectionsUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\immutablecollectionsutils.cs
++JavaScriptUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\javascriptutils.cs
++JsonTokenUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\jsontokenutils.cs
++LateBoundReflectionDelegateFactory.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\lateboundreflectiondelegatefactory.cs
++LinqBridge.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\linqbridge.cs
++MathUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\mathutils.cs
++MethodBinder.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\methodbinder.cs
++MethodCall.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\methodcall.cs
++MiscellaneousUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\miscellaneousutils.cs
++NullableAttributes.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\nullableattributes.cs
++ReflectionDelegateFactory.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\reflectiondelegatefactory.cs
++ReflectionObject.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\reflectionobject.cs
++ReflectionUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\reflectionutils.cs
++StringBuffer.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\stringbuffer.cs
++StringReference.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\stringreference.cs
++StringUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\stringutils.cs
++StructMultiKey.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\structmultikey.cs
++ThreadSafeStore.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\threadsafestore.cs
++TypeExtensions.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\typeextensions.cs
++ValidationUtils.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\utilities\validationutils.cs
++ArrayIndexFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\arrayindexfilter.cs
++ArrayMultipleIndexFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\arraymultipleindexfilter.cs
++ArraySliceFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\arrayslicefilter.cs
++FieldFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\fieldfilter.cs
++FieldMultipleFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\fieldmultiplefilter.cs
++JPath.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\jpath.cs
++PathFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\pathfilter.cs
++QueryExpression.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\queryexpression.cs
++QueryFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\queryfilter.cs
++QueryScanFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\queryscanfilter.cs
++RootFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\rootfilter.cs
++ScanFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\scanfilter.cs
++ScanMultipleFilter.cs
i:{bb3cbf77-2f51-489e-443e-346b4cfd4ff9}:e:\unity\human_opensource\assets\scripts\newtonsoft.json\linq\jsonpath\scanmultiplefilter.cs
