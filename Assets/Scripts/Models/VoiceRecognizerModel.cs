using LKZ.Commands.Voice;
using LKZ.DependencyInject;
using LKZ.TypeEventSystem;
using System;
using System.Collections;
using System.IO.Compression;
using System.IO;
using System.Net.WebSockets;
using System.Text;
using UnityEngine;

namespace LKZ.Voice
{
    public sealed class VoiceRecognizerModel
    {
        const string url= "ws://47.79.16.41:10199";

        [Inject]
        private MonoBehaviour _mono { get; set; }

        [Inject]
        private ISendCommand SendCommand { get; set; }

        [Inject]
        private IRegisterCommand RegisterCommand { get; set; }


        private VoiceRecognitionResultCommand voiceRecognitionResult = new VoiceRecognitionResultCommand();

        VoiceRecognizerBase voiceRecognizer;
        public void Initialized()
        {
            RegisterCommand.Register<SettingVoiceRecognitionCommand>(SettingVoiceRecognitionCommandCallback);

#if UNITY_WEBGL && !UNITY_EDITOR
            voiceRecognizer = new VoiceRecognizerWebGL();
#else
            voiceRecognizer = new VoiceRecognizerNoWebGL();
#endif
            voiceRecognizer.Initialized(this._mono, url, this.DisponseRecognition);
        }
         
        /// <summary>
        /// ��������ʶ������ص�
        /// </summary>
        /// <param name="obj"></param>
        private void SettingVoiceRecognitionCommandCallback(SettingVoiceRecognitionCommand obj)
        {
            voiceRecognizer.SetIsRecogition(obj.IsStartVoiceRecognition);
        }

        /// <summary>
        /// ����ʶ����
        /// </summary>
        /// <param name="count"></param>
        private void DisponseRecognition(string text1)
        {  
            if (text1 == " N" || text1 == "N" || text1 == "A" || text1 == " A")
                return;

            if (!string.IsNullOrEmpty(text1))
            {
                if (text1 == "\n")
                {
                    voiceRecognitionResult.IsComplete = true;
                    voiceRecognitionResult.text = string.Empty;
                }
                else
                {
                    voiceRecognitionResult.IsComplete = false;
                    voiceRecognitionResult.text = text1;
                }

                SendCommand.Send(voiceRecognitionResult);
            }
        }

        public void OnDestroy()
        { 
        }
    }


    public abstract class VoiceRecognizerBase
    {
        public abstract void Initialized(MonoBehaviour _mono,string websocketUrl, Action<string> _recognizerCallback);

        public abstract void SetIsRecogition(bool IsRecogition);
    } 

#if UNITY_EDITOR ||UNITY_STANDALONE || UNITY_ANDROID

    public sealed class VoiceRecognizerNoWebGL : VoiceRecognizerBase
    {
        ClientWebSocket webSocket;

        AudioClip microphoneClip;


        /// <summary>
        /// ������˷���ʱ��
        /// </summary>
        WaitForSeconds samplingInterval = new WaitForSeconds(1 / 5f);

        private MonoBehaviour _mono;

        private Action<string> recognizerCallback;
        private bool IsRecogition;

        public override async void Initialized(MonoBehaviour _mono, string websocketUrl, Action<string> _recognizerCallback)
        {
            this._mono = _mono;
            recognizerCallback = _recognizerCallback;

            webSocket = new ClientWebSocket();
            await webSocket.ConnectAsync(new Uri(websocketUrl), default);

            _mono.StartCoroutine(InitializedMicrophone());
            byte[] p = new byte[1024 * 1024];
            int count = 0;
            while (true)
            {
                var result = await webSocket.ReceiveAsync(new ArraySegment<byte>(p, count, p.Length - count), default);
                count += result.Count;
                if (result.EndOfMessage)
                {
                    var str = Encoding.UTF8.GetString(p, 0, count);
                    recognizerCallback?.Invoke(str);
                    count = 0;
                }
            }
        }

        public override void SetIsRecogition(bool IsRecogition)
        {
            this.IsRecogition = IsRecogition;
            if (IsRecogition)
                this.lastSampling = Microphone.GetPosition(null);

        }

        IEnumerator InitializedMicrophone()
        {
            yield return Application.RequestUserAuthorization(UserAuthorization.Microphone);
            if (Application.HasUserAuthorization(UserAuthorization.Microphone))
            {
                do
                {
                    microphoneClip = Microphone.Start(null, true, 1, 16000);
                    yield return null;
                } while (!Microphone.IsRecording(null));

                _mono.StartCoroutine(MicrophoneSamplingRecognition());
            }
            else
            {
                Debug.Log("����Ȩ��˷�Ȩ�ޣ�");
            }
        }


        /// <summary>
        /// ��һ�β���λ��
        /// </summary>
        int lastSampling;

        float[] f = new float[16000];
        IEnumerator MicrophoneSamplingRecognition()
        {
            while (true)
            {
                yield return samplingInterval;
                if (!IsRecogition)
                    continue;

                int currentPos = Microphone.GetPosition(null);
                bool isSucceed = microphoneClip.GetData(f, 0);

                if (isSucceed)
                    if (lastSampling != currentPos)
                    {
                        int count = 0;
                        float[] p = default;
                        if (currentPos > lastSampling)
                        {
                            count = currentPos - lastSampling;
                            p = new float[count]; 
                            Array.Copy(f, lastSampling, p, 0, count);
                        }
                        else
                        {
                            count = 16000 - lastSampling;
                            p = new float[count + currentPos]; 
                            Array.Copy(f, lastSampling, p, 0, count);
                             
                            Array.Copy(f, 0, p, count, currentPos);

                            count += currentPos;
                        }

                        lastSampling = currentPos;
                        DisponseRecognition(p);
                    }

            }
        }

        private void DisponseRecognition(float[] p)
        {
            var buffer = FloatArrayToByteArray(p);

            
            this.webSocket.SendAsync(new ArraySegment<byte>(buffer), WebSocketMessageType.Binary, true, default);
        }


        byte[] FloatArrayToByteArray(in float[] floatArray)
        {
            int byteCount = floatArray.Length * sizeof(float);
            byte[] byteArray = new byte[byteCount];

            Buffer.BlockCopy(floatArray, 0, byteArray, 0, byteCount);

            return byteArray;
        }

        static byte[] Compress(in byte[] data)
        {
            using (MemoryStream compressedStream = new MemoryStream())
            {
                using (GZipStream gzipStream = new GZipStream(compressedStream, CompressionMode.Compress))
                {
                    gzipStream.Write(data, 0, data.Length);
                }
                return compressedStream.ToArray();
            }
        }
    }
#endif

#if UNITY_WEBGL && !UNITY_EDITOR
    public sealed class VoiceRecognizerWebGL : VoiceRecognizerBase
    {
        private static VoiceRecognizerWebGL instance;
        private MonoBehaviour _mono;
        private Action<string> recognizerCallback;
        private bool IsRecognition;
        private string websocketUrl;

        public override void Initialized(MonoBehaviour _mono, string websocketUrl, Action<string> _recognizerCallback)
        {
            this._mono = _mono;
            this.websocketUrl = websocketUrl;
            recognizerCallback = _recognizerCallback;
            instance = this; // 保存实例以供JavaScript回调使用

            // 初始化WebGL音频系统
            WebGLAudioInit();
        }

        public override void SetIsRecogition(bool IsRecognition)
        {
            this.IsRecognition = IsRecognition;
            if (IsRecognition)
            {
                WebGLStartRecording(websocketUrl);
            }
            else
            {
                WebGLStopRecording();
            }
        }

        // JavaScript插件方法声明
        [System.Runtime.InteropServices.DllImport("__Internal")]
        private static extern void WebGLAudioInit();

        [System.Runtime.InteropServices.DllImport("__Internal")]
        private static extern void WebGLStartRecording(string websocketUrl);

        [System.Runtime.InteropServices.DllImport("__Internal")]
        private static extern void WebGLStopRecording();

        // 这个方法会被JavaScript调用 - 需要是静态方法
        public static void OnRecognitionResult(string result)
        {
            if (instance != null)
            {
                instance.recognizerCallback?.Invoke(result);
            }
        }
    }
#endif
}