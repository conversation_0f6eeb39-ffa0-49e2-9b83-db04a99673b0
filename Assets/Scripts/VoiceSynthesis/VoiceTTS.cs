﻿using LKZ.Utilitys;
using System.Collections;
using UnityEngine;
using UnityEngine.Networking;

namespace LKZ.VoiceSynthesis
{ 

    /// <summary>
    ///  语音合成
    /// </summary>
    public static class VoiceTTS
    {
        public const string ErrorMess = "语音识别出错了";

        static int voiceID;
        public static int VoiceID
        {
            get => voiceID; set
            {
                voiceID = value;
                DataSave.SetVoiceID(voiceID);
            }
        }

        static VoiceTTS()
        {
            voiceID = DataSave.GetVoiceID();
        }

        public static IEnumerator Synthesis(string content)
        {
            // 尝试多种音频格式以确保兼容性
            AudioType[] audioTypes = GetSupportedAudioTypes();

            foreach (var audioType in audioTypes)
            {
                string formatParam = GetFormatParameter(audioType);
                string url = $"http://***********:9001/tts?content={content}&id={VoiceID}{formatParam}";

                using (var request = UnityWebRequestMultimedia.GetAudioClip(url, audioType))
                {
                    // WebGL平台需要设置超时时间
#if UNITY_WEBGL && !UNITY_EDITOR
                    request.timeout = 30; // 30秒超时
#endif
                    var result = request.SendWebRequest();
                    while (!result.isDone)
                    {
                        yield return null;
                    }

                    if (request.result == UnityWebRequest.Result.Success)
                    {
                        AudioClip _audioClip = DownloadHandlerAudioClip.GetContent(request);
                        if (_audioClip != null)
                        {
                            yield return _audioClip;
                            yield break; // 成功获取音频，退出循环
                        }
                    }

                    Debug.LogWarning($"TTS格式 {audioType} 失败: {request.error}，尝试下一种格式");
                }
            }

            // 所有格式都失败了
            Debug.LogError("TTS: 所有音频格式都失败了");
            yield return ErrorMess;
        }

        /// <summary>
        /// 获取当前平台支持的音频格式列表（按优先级排序）
        /// </summary>
        /// <returns></returns>
        private static AudioType[] GetSupportedAudioTypes()
        {
#if UNITY_WEBGL && !UNITY_EDITOR
            // WebGL平台支持的格式，按兼容性优先级排序
            return new AudioType[] {
                AudioType.OGGVORBIS,  // 最佳兼容性
                AudioType.WAV,        // 备选方案
                AudioType.MPEG        // 最后尝试
            };
#else
            // 其他平台支持的格式
            return new AudioType[] {
                AudioType.MPEG,       // 首选
                AudioType.OGGVORBIS,  // 备选
                AudioType.WAV         // 最后尝试
            };
#endif
        }

        /// <summary>
        /// 根据音频格式获取URL参数
        /// </summary>
        /// <param name="audioType"></param>
        /// <returns></returns>
        private static string GetFormatParameter(AudioType audioType)
        {
            switch (audioType)
            {
                case AudioType.OGGVORBIS:
                    return "&format=ogg";
                case AudioType.WAV:
                    return "&format=wav";
                case AudioType.MPEG:
                default:
                    return ""; // 默认格式，不需要额外参数
            }
        }
    }
}
