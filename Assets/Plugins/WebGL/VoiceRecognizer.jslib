mergeInto(LibraryManager.library, {
    WebGLAudioInit: function() {
        window.unityVoiceRecognizer = {
            mediaRecorder: null,
            websocket: null,
            audioContext: null,
            isRecording: false,
            asrStarted: false,

            init: function() {
                // 初始化音频上下文
                if (!this.audioContext) {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }
            }
        };
        
        window.unityVoiceRecognizer.init();
    },

    WebGLStartRecording: function(websocketUrlPtr) {
        var websocketUrl = UTF8ToString(websocketUrlPtr);
        var recognizer = window.unityVoiceRecognizer;
        
        if (recognizer.isRecording) {
            return;
        }

        // 重置ASR状态
        recognizer.asrStarted = false;
        
        // 连接WebSocket
        try {
            recognizer.websocket = new WebSocket(websocketUrl);
            
            recognizer.websocket.onopen = function() {
                console.log('WebSocket连接已建立');
                // 发送启动ASR命令
                var startCmd = {
                    action: 'start',
                    mode: 'ali'  // 或 'ali'
                };
                recognizer.websocket.send(JSON.stringify(startCmd));
                console.log('已发送ASR启动命令');
            };
            
            recognizer.websocket.onmessage = function(event) {
                // 处理ASR服务端返回的消息
                try {
                    var data = event.data;

                    // 解析JSON格式的响应
                    if (typeof data === 'string') {
                        var jsonData = JSON.parse(data);

                        if (jsonData.status === 'started') {
                            console.log('ASR已启动，开始录音');
                            recognizer.asrStarted = true;
                        } else if (jsonData.type === 'result') {
                            // 实时识别结果
                            console.log('实时识别结果:', jsonData.text);
                            SendMessage('GameApp', 'OnWebGLRecognitionResult', jsonData.text);
                        } else if (jsonData.type === 'final') {
                            // 最终识别结果
                            console.log('最终识别结果:', jsonData.text);
                            SendMessage('GameApp', 'OnWebGLRecognitionResult', jsonData.text);
                        } else {
                            console.log('收到其他消息:', jsonData);
                        }
                    } else {
                        console.warn('收到非字符串消息:', data);
                    }
                } catch (e) {
                    console.error('处理识别结果失败:', e, '原始数据:', event.data);
                }
            };
            
            recognizer.websocket.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
            
            recognizer.websocket.onclose = function() {
                console.log('WebSocket连接已关闭');
            };
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            return;
        }
        
        // 请求麦克风权限并开始录音
        navigator.mediaDevices.getUserMedia({
            audio: {
                sampleRate: 16000,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true
            }
        })
        .then(function(stream) {
            recognizer.isRecording = true;

            // 使用AudioContext进行实时音频处理
            var audioContext = recognizer.audioContext;
            var source = audioContext.createMediaStreamSource(stream);
            var processor = audioContext.createScriptProcessor(1024, 1, 1);

            // 音频数据处理
            processor.onaudioprocess = function(event) {
                if (!recognizer.isRecording || !recognizer.websocket || recognizer.websocket.readyState !== WebSocket.OPEN) {
                    return;
                }

                // 确保ASR已启动后再发送音频数据
                if (!recognizer.asrStarted) {
                    return;
                }

                var inputBuffer = event.inputBuffer;
                var inputData = inputBuffer.getChannelData(0);

                // 将Float32Array转换为16位PCM数据
                var pcmData = new Int16Array(inputData.length);
                for (var i = 0; i < inputData.length; i++) {
                    var sample = Math.max(-1, Math.min(1, inputData[i]));
                    pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
                }

                // 发送PCM数据
                recognizer.websocket.send(pcmData.buffer);
            };

            // 连接音频节点
            source.connect(processor);
            processor.connect(audioContext.destination);

            // 保存引用以便清理
            recognizer.audioSource = source;
            recognizer.audioProcessor = processor;
            recognizer.mediaStream = stream;

        })
        .catch(function(error) {
            console.error('获取麦克风权限失败:', error);
            recognizer.isRecording = false;
        });
    },

    WebGLStopRecording: function() {
        var recognizer = window.unityVoiceRecognizer;

        if (!recognizer.isRecording) {
            return;
        }

        recognizer.isRecording = false;
        recognizer.asrStarted = false;

        // 发送停止ASR命令
        if (recognizer.websocket && recognizer.websocket.readyState === WebSocket.OPEN) {
            var stopCmd = {action: 'stop'};
            recognizer.websocket.send(JSON.stringify(stopCmd));
            console.log('已发送ASR停止命令');
        }

        // 断开音频节点连接
        if (recognizer.audioProcessor) {
            recognizer.audioProcessor.disconnect();
            recognizer.audioProcessor = null;
        }

        if (recognizer.audioSource) {
            recognizer.audioSource.disconnect();
            recognizer.audioSource = null;
        }

        // 停止媒体流
        if (recognizer.mediaStream) {
            recognizer.mediaStream.getTracks().forEach(function(track) {
                track.stop();
            });
            recognizer.mediaStream = null;
        }

        // 关闭WebSocket连接
        if (recognizer.websocket && recognizer.websocket.readyState === WebSocket.OPEN) {
            recognizer.websocket.close();
        }

        recognizer.websocket = null;
    }
});
