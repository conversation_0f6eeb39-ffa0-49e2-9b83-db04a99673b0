mergeInto(LibraryManager.library, {
    WebGLAudioInit: function() {
        window.unityVoiceRecognizer = {
            mediaRecorder: null,
            websocket: null,
            audioContext: null,
            isRecording: false,
            asrStarted: false,

            init: function() {
                // 初始化音频上下文
                if (!this.audioContext) {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }
            },

            reconnectWebSocket: function(websocketUrl) {
                var self = this;
                try {
                    // 关闭旧连接
                    if (this.websocket) {
                        this.websocket.close();
                    }

                    // 创建新连接
                    this.websocket = new WebSocket(websocketUrl);

                    this.websocket.onopen = function() {
                        console.log('WebSocket重连成功');
                        // 重新发送启动命令
                        var startCmd = {
                            action: 'start',
                            mode: 'ali'
                        };
                        self.websocket.send(JSON.stringify(startCmd));
                        console.log('重连后已发送ASR启动命令');
                    };

                    // 复用原来的消息处理逻辑
                    this.websocket.onmessage = this.handleWebSocketMessage;
                    this.websocket.onerror = this.handleWebSocketError;
                    this.websocket.onclose = this.handleWebSocketClose;

                } catch (error) {
                    console.error('WebSocket重连失败:', error);
                }
            }
        };
        
        window.unityVoiceRecognizer.init();
    },

    WebGLStartRecording: function(websocketUrlPtr) {
        var websocketUrl = UTF8ToString(websocketUrlPtr);
        var recognizer = window.unityVoiceRecognizer;
        
        if (recognizer.isRecording) {
            return;
        }

        // 重置ASR状态
        recognizer.asrStarted = false;
        
        // 连接WebSocket
        try {
            recognizer.websocket = new WebSocket(websocketUrl);
            
            recognizer.websocket.onopen = function() {
                console.log('WebSocket连接已建立');
                // 发送启动ASR命令
                var startCmd = {
                    action: 'start',
                    mode: 'ali'  // 或 'ali'
                };
                recognizer.websocket.send(JSON.stringify(startCmd));
                console.log('已发送ASR启动命令');
            };
            
            // 定义可复用的事件处理函数
            recognizer.handleWebSocketMessage = function(event) {
                // 处理ASR服务端返回的消息
                try {
                    var data = event.data;
                    console.log('收到服务端消息:', data); // 添加调试信息

                    // 解析JSON格式的响应
                    if (typeof data === 'string') {
                        var jsonData = JSON.parse(data);
                        console.log('解析后的JSON数据:', jsonData); // 添加调试信息

                        // 检查多种可能的启动确认格式
                        if (jsonData.status === 'started' ||
                            jsonData.action === 'started' ||
                            (jsonData.header && jsonData.header.name === 'TranscriptionStarted')) {
                            console.log('ASR已启动，开始录音');
                            recognizer.asrStarted = true;
                        } else if (jsonData.type === 'result' ||
                                   (jsonData.header && jsonData.header.name === 'TranscriptionResultChanged')) {
                            // 实时识别结果
                            var resultText = jsonData.text || (jsonData.payload && jsonData.payload.result) || '';
                            console.log('实时识别结果:', resultText);
                            if (resultText) {
                                SendMessage('GameApp', 'OnWebGLRecognitionResult', resultText);
                            }
                        } else if (jsonData.type === 'final' ||
                                   (jsonData.header && jsonData.header.name === 'SentenceEnd')) {
                            // 最终识别结果
                            var finalText = jsonData.text || (jsonData.payload && jsonData.payload.result) || '';
                            console.log('最终识别结果:', finalText);
                            if (finalText) {
                                SendMessage('GameApp', 'OnWebGLRecognitionResult', finalText);
                            }
                        } else {
                            console.log('收到其他消息:', jsonData);
                            // 如果还没启动ASR，尝试强制启动
                            if (!recognizer.asrStarted) {
                                console.log('强制启动ASR音频发送');
                                recognizer.asrStarted = true;
                            }
                        }
                    } else {
                        console.warn('收到非字符串消息:', data);
                    }
                } catch (e) {
                    console.error('处理识别结果失败:', e, '原始数据:', event.data);
                }
            };

            recognizer.handleWebSocketError = function(error) {
                console.error('WebSocket错误:', error);
            };

            recognizer.handleWebSocketClose = function(event) {
                console.log('WebSocket连接已关闭, code:', event.code, 'reason:', event.reason);
                recognizer.asrStarted = false;

                // 如果是异常关闭且还在录音中，尝试重连
                if (recognizer.isRecording && event.code !== 1000) {
                    console.log('检测到异常关闭，3秒后尝试重连...');
                    setTimeout(function() {
                        if (recognizer.isRecording) {
                            console.log('尝试重新连接WebSocket...');
                            recognizer.reconnectWebSocket(websocketUrl);
                        }
                    }, 3000);
                }
            };

            // 绑定事件处理函数
            recognizer.websocket.onmessage = recognizer.handleWebSocketMessage;
            recognizer.websocket.onerror = recognizer.handleWebSocketError;
            recognizer.websocket.onclose = recognizer.handleWebSocketClose;
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            return;
        }
        
        // 请求麦克风权限并开始录音
        navigator.mediaDevices.getUserMedia({
            audio: {
                sampleRate: 16000,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true
            }
        })
        .then(function(stream) {
            recognizer.isRecording = true;

            // 使用AudioContext进行实时音频处理
            var audioContext = recognizer.audioContext;
            var source = audioContext.createMediaStreamSource(stream);
            var processor = audioContext.createScriptProcessor(1024, 1, 1);

            // 音频数据处理
            processor.onaudioprocess = function(event) {
                if (!recognizer.isRecording || !recognizer.websocket || recognizer.websocket.readyState !== WebSocket.OPEN) {
                    console.log('音频处理跳过: isRecording=' + recognizer.isRecording +
                               ', websocket=' + (recognizer.websocket ? 'exists' : 'null') +
                               ', readyState=' + (recognizer.websocket ? recognizer.websocket.readyState : 'N/A'));
                    return;
                }

                // 确保ASR已启动后再发送音频数据
                if (!recognizer.asrStarted) {
                    console.log('等待ASR启动...');
                    return;
                }

                var inputBuffer = event.inputBuffer;
                var inputData = inputBuffer.getChannelData(0);

                // 检查音频数据是否有效
                var hasAudio = false;
                for (var i = 0; i < inputData.length; i++) {
                    if (Math.abs(inputData[i]) > 0.01) {
                        hasAudio = true;
                        break;
                    }
                }

                if (hasAudio) {
                    console.log('检测到音频输入，发送PCM数据');
                }

                // 将Float32Array转换为16位PCM数据
                var pcmData = new Int16Array(inputData.length);
                for (var i = 0; i < inputData.length; i++) {
                    var sample = Math.max(-1, Math.min(1, inputData[i]));
                    pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
                }

                // 发送PCM数据
                try {
                    recognizer.websocket.send(pcmData.buffer);
                    if (hasAudio) {
                        console.log('PCM数据已发送，长度:', pcmData.length);
                    }
                } catch (e) {
                    console.error('发送PCM数据失败:', e);
                }
            };

            // 连接音频节点
            source.connect(processor);
            processor.connect(audioContext.destination);

            // 保存引用以便清理
            recognizer.audioSource = source;
            recognizer.audioProcessor = processor;
            recognizer.mediaStream = stream;

        })
        .catch(function(error) {
            console.error('获取麦克风权限失败:', error);
            recognizer.isRecording = false;
        });
    },

    WebGLStopRecording: function() {
        var recognizer = window.unityVoiceRecognizer;

        if (!recognizer.isRecording) {
            return;
        }

        recognizer.isRecording = false;
        recognizer.asrStarted = false;

        // 发送停止ASR命令
        if (recognizer.websocket && recognizer.websocket.readyState === WebSocket.OPEN) {
            var stopCmd = {action: 'stop'};
            recognizer.websocket.send(JSON.stringify(stopCmd));
            console.log('已发送ASR停止命令');
        }

        // 断开音频节点连接
        if (recognizer.audioProcessor) {
            recognizer.audioProcessor.disconnect();
            recognizer.audioProcessor = null;
        }

        if (recognizer.audioSource) {
            recognizer.audioSource.disconnect();
            recognizer.audioSource = null;
        }

        // 停止媒体流
        if (recognizer.mediaStream) {
            recognizer.mediaStream.getTracks().forEach(function(track) {
                track.stop();
            });
            recognizer.mediaStream = null;
        }

        // 关闭WebSocket连接
        if (recognizer.websocket && recognizer.websocket.readyState === WebSocket.OPEN) {
            recognizer.websocket.close();
        }

        recognizer.websocket = null;
    }
});
