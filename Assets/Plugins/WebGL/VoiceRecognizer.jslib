mergeInto(LibraryManager.library, {
    WebGLAudioInit: function() {
        window.unityVoiceRecognizer = {
            mediaRecorder: null,
            websocket: null,
            audioContext: null,
            isRecording: false,
            asrStarted: false,
            taskId: '',
            appKey: '',
            token: '',
            accessKeyId: '',
            accessKeySecret: '',

            init: function() {
                // 初始化音频上下文
                if (!this.audioContext) {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }
            },

            // 生成随机32位十六进制字符串
            generateRandomHex: function(length) {
                var result = '';
                var characters = '0123456789abcdef';
                for (var i = 0; i < length; i++) {
                    result += characters.charAt(Math.floor(Math.random() * characters.length));
                }
                return result;
            },

            // 创建阿里云NLS消息头
            createHeader: function(name) {
                if (name === 'StartTranscription') {
                    this.taskId = this.generateRandomHex(32);
                }
                return {
                    "appkey": this.appKey,
                    "message_id": this.generateRandomHex(32),
                    "task_id": this.taskId,
                    "namespace": "SpeechTranscriber",
                    "name": name
                };
            },

            // 获取阿里云Token
            getAliToken: function() {
                var self = this;

                // 构建请求参数
                var requestData = {
                    'AccessKeyId': this.accessKeyId,
                    'Action': 'CreateToken',
                    'Format': 'JSON',
                    'RegionId': 'cn-shanghai',
                    'SignatureMethod': 'HMAC-SHA1',
                    'SignatureNonce': this.generateRandomHex(16),
                    'SignatureVersion': '1.0',
                    'Timestamp': new Date().toISOString(),
                    'Version': '2019-02-28'
                };

                // 计算签名并发送请求
                this.sendAliRequest(requestData);
            },

            // 发送阿里云API请求
            sendAliRequest: function(params) {
                var self = this;

                try {
                    // 构建请求URL
                    var url = 'https://nls-meta.cn-shanghai.aliyuncs.com/';

                    // 计算签名
                    var signature = this.calculateSignature(params);
                    params['Signature'] = signature;

                    // 构建POST数据
                    var formData = new FormData();
                    for (var key in params) {
                        formData.append(key, params[key]);
                    }

                    // 发送请求
                    fetch(url, {
                        method: 'POST',
                        body: formData
                    })
                    .then(function(response) {
                        return response.json();
                    })
                    .then(function(data) {
                        if (data.Token && data.Token.Id) {
                            self.token = data.Token.Id;
                            self.appKey = self.appKey; // 使用预设的AppKey
                            console.log('阿里云Token获取成功');
                            SendMessage('GameApp', 'OnTokenReceived', self.token);
                        } else {
                            console.error('Token获取失败:', data);
                            SendMessage('GameApp', 'OnTokenError', JSON.stringify(data));
                        }
                    })
                    .catch(function(error) {
                        console.error('Token请求失败:', error);
                        SendMessage('GameApp', 'OnTokenError', error.toString());
                    });

                } catch (e) {
                    console.error('Token获取异常:', e);
                    SendMessage('GameApp', 'OnTokenError', e.toString());
                }
            },

            // 计算阿里云API签名
            calculateSignature: function(params) {
                // 注意：这是一个简化的签名实现
                // 实际生产环境中建议使用专门的签名库
                var sortedKeys = Object.keys(params).sort();
                var canonicalizedQueryString = '';

                for (var i = 0; i < sortedKeys.length; i++) {
                    var key = sortedKeys[i];
                    if (i > 0) canonicalizedQueryString += '&';
                    canonicalizedQueryString += encodeURIComponent(key) + '=' + encodeURIComponent(params[key]);
                }

                var stringToSign = 'POST&' + encodeURIComponent('/') + '&' + encodeURIComponent(canonicalizedQueryString);

                // 使用HMAC-SHA1计算签名（需要crypto-js库或浏览器原生支持）
                // 这里返回一个占位符，实际需要实现HMAC-SHA1
                return 'placeholder_signature';
            }
        };
        
        window.unityVoiceRecognizer.init();
    },

    WebGLStartRecording: function(websocketUrlPtr) {
        var websocketUrl = UTF8ToString(websocketUrlPtr);
        var recognizer = window.unityVoiceRecognizer;
        
        if (recognizer.isRecording) {
            return;
        }

        // 重置ASR状态
        recognizer.asrStarted = false;
        
        // 构建带Token的WebSocket URL
        var baseUrl = UTF8ToString(url);
        var wsUrl = baseUrl + '?token=' + recognizer.token;

        // 连接WebSocket
        try {
            recognizer.websocket = new WebSocket(wsUrl);
            
            recognizer.websocket.onopen = function() {
                console.log('WebSocket连接已建立');
                // 发送阿里云NLS启动命令
                var startCmd = {
                    'header': recognizer.createHeader('StartTranscription'),
                    'payload': {
                        'format': 'pcm',
                        'sample_rate': 16000,
                        'enable_intermediate_result': true,
                        'enable_punctuation_prediction': false,
                        'enable_inverse_text_normalization': true,
                        'speech_noise_threshold': -1
                    }
                };
                recognizer.websocket.send(JSON.stringify(startCmd));
                console.log('已发送阿里云NLS启动命令');
            };
            
            recognizer.websocket.onmessage = function(event) {
                // 处理阿里云NLS返回的消息
                try {
                    var data = event.data;

                    // 解析JSON格式的响应
                    if (typeof data === 'string') {
                        var jsonData = JSON.parse(data);
                        var header = jsonData.header;
                        var name = header ? header.name : '';

                        if (name === 'TranscriptionStarted') {
                            console.log('阿里云NLS已启动，开始录音');
                            recognizer.asrStarted = true;
                        } else if (name === 'TranscriptionResultChanged') {
                            // 实时识别结果
                            var result = jsonData.payload ? jsonData.payload.result : '';
                            console.log('实时识别结果:', result);
                            SendMessage('GameApp', 'OnWebGLRecognitionResult', result);
                        } else if (name === 'SentenceEnd') {
                            // 最终识别结果
                            var result = jsonData.payload ? jsonData.payload.result : '';
                            console.log('最终识别结果:', result);
                            SendMessage('GameApp', 'OnWebGLRecognitionResult', result);
                        } else {
                            console.log('收到其他阿里云NLS消息:', jsonData);
                        }
                    } else {
                        console.warn('收到非字符串消息:', data);
                    }
                } catch (e) {
                    console.error('处理阿里云NLS识别结果失败:', e, '原始数据:', event.data);
                }
            };
            
            recognizer.websocket.onerror = function(error) {
                console.error('WebSocket错误:', error);
            };
            
            recognizer.websocket.onclose = function() {
                console.log('WebSocket连接已关闭');
            };
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            return;
        }
        
        // 请求麦克风权限并开始录音
        navigator.mediaDevices.getUserMedia({
            audio: {
                sampleRate: 16000,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true
            }
        })
        .then(function(stream) {
            recognizer.isRecording = true;

            // 使用AudioContext进行实时音频处理
            var audioContext = recognizer.audioContext;
            var source = audioContext.createMediaStreamSource(stream);
            var processor = audioContext.createScriptProcessor(1024, 1, 1);

            // 音频数据处理
            processor.onaudioprocess = function(event) {
                if (!recognizer.isRecording || !recognizer.websocket || recognizer.websocket.readyState !== WebSocket.OPEN) {
                    return;
                }

                // 确保ASR已启动后再发送音频数据
                if (!recognizer.asrStarted) {
                    return;
                }

                var inputBuffer = event.inputBuffer;
                var inputData = inputBuffer.getChannelData(0);

                // 将Float32Array转换为16位PCM数据
                var pcmData = new Int16Array(inputData.length);
                for (var i = 0; i < inputData.length; i++) {
                    var sample = Math.max(-1, Math.min(1, inputData[i]));
                    pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
                }

                // 发送PCM数据
                recognizer.websocket.send(pcmData.buffer);
            };

            // 连接音频节点
            source.connect(processor);
            processor.connect(audioContext.destination);

            // 保存引用以便清理
            recognizer.audioSource = source;
            recognizer.audioProcessor = processor;
            recognizer.mediaStream = stream;

        })
        .catch(function(error) {
            console.error('获取麦克风权限失败:', error);
            recognizer.isRecording = false;
        });
    },

    WebGLSetAliCredentials: function(accessKeyIdPtr, accessKeySecretPtr, appKeyPtr) {
        var recognizer = window.unityVoiceRecognizer;
        recognizer.accessKeyId = UTF8ToString(accessKeyIdPtr);
        recognizer.accessKeySecret = UTF8ToString(accessKeySecretPtr);
        recognizer.appKey = UTF8ToString(appKeyPtr);
        console.log('已设置阿里云凭证');
    },

    WebGLGetAliToken: function() {
        var recognizer = window.unityVoiceRecognizer;
        recognizer.getAliToken();
    },

    WebGLStopRecording: function() {
        var recognizer = window.unityVoiceRecognizer;

        if (!recognizer.isRecording) {
            return;
        }

        recognizer.isRecording = false;
        recognizer.asrStarted = false;

        // 发送阿里云NLS停止命令
        if (recognizer.websocket && recognizer.websocket.readyState === WebSocket.OPEN) {
            var stopCmd = {
                'header': recognizer.createHeader('StopTranscription')
            };
            recognizer.websocket.send(JSON.stringify(stopCmd));
            console.log('已发送阿里云NLS停止命令');
        }

        // 断开音频节点连接
        if (recognizer.audioProcessor) {
            recognizer.audioProcessor.disconnect();
            recognizer.audioProcessor = null;
        }

        if (recognizer.audioSource) {
            recognizer.audioSource.disconnect();
            recognizer.audioSource = null;
        }

        // 停止媒体流
        if (recognizer.mediaStream) {
            recognizer.mediaStream.getTracks().forEach(function(track) {
                track.stop();
            });
            recognizer.mediaStream = null;
        }

        // 关闭WebSocket连接
        if (recognizer.websocket && recognizer.websocket.readyState === WebSocket.OPEN) {
            recognizer.websocket.close();
        }

        recognizer.websocket = null;
    }
});
