mergeInto(LibraryManager.library, {
    WebGLAudioInit: function() {
        window.unityVoiceRecognizer = {
            mediaRecorder: null,
            websocket: null,
            audioContext: null,
            isRecording: false,
            asrStarted: false,

            // 语音活动检测相关
            isSpeaking: false,
            silenceCount: 0,
            speechCount: 0,
            silenceThreshold: 30, // 静音帧数阈值（约0.7秒）
            speechThreshold: 5,   // 语音帧数阈值（约0.1秒）
            volumeThreshold: 0.01, // 音量阈值

            // 语音交互流程控制
            speechSessionActive: false,  // 是否在语音会话中
            waitingForFinalResult: false, // 是否等待最终识别结果
            currentRecognitionText: '',   // 当前识别的文本
            speechEndTime: 0,            // 语音结束时间

            // 流控制相关
            isWaitingResponse: false,
            lastSendTime: 0,
            sendInterval: 200, // 最小发送间隔(ms) - 增加到200ms避免限流
            maxSendRate: 5,    // 每秒最大发送次数
            sendCount: 0,      // 当前秒内发送次数
            lastSecond: 0,     // 上一秒的时间戳

            init: function() {
                // 初始化音频上下文
                if (!this.audioContext) {
                    this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                }
            },

            reconnectWebSocket: function(websocketUrl) {
                var self = this;
                try {
                    // 关闭旧连接
                    if (this.websocket) {
                        this.websocket.close();
                    }

                    // 重置发送计数
                    this.sendCount = 0;
                    this.lastSendTime = 0;
                    this.lastSecond = 0;

                    // 创建新连接
                    this.websocket = new WebSocket(websocketUrl);

                    this.websocket.onopen = function() {
                        console.log('WebSocket重连成功');
                        // 重新发送启动命令
                        var startCmd = {
                            action: 'start',
                            mode: 'ali'
                        };
                        self.websocket.send(JSON.stringify(startCmd));
                        console.log('重连后已发送ASR启动命令');
                    };

                    // 复用原来的消息处理逻辑
                    this.websocket.onmessage = this.handleWebSocketMessage;
                    this.websocket.onerror = this.handleWebSocketError;
                    this.websocket.onclose = this.handleWebSocketClose;

                } catch (error) {
                    console.error('WebSocket重连失败:', error);
                }
            },

            // 语音活动检测
            detectVoiceActivity: function(audioData) {
                // 计算音频能量
                var sum = 0;
                for (var i = 0; i < audioData.length; i++) {
                    sum += audioData[i] * audioData[i];
                }
                var rms = Math.sqrt(sum / audioData.length);

                // 判断是否有语音
                var hasVoice = rms > this.volumeThreshold;

                if (hasVoice) {
                    this.speechCount++;
                    this.silenceCount = 0;

                    // 连续检测到语音，开始说话
                    if (!this.isSpeaking && this.speechCount >= this.speechThreshold) {
                        this.isSpeaking = true;
                        this.speechSessionActive = true;
                        console.log('🎤 检测到开始说话，启动语音会话');
                        this.isWaitingResponse = false; // 重新开始说话时重置等待状态
                        this.currentRecognitionText = ''; // 清空之前的识别文本

                        // 通知Unity开始语音输入
                        SendMessage('GameApp', 'OnSpeechStart', '');
                    }
                } else {
                    this.silenceCount++;
                    this.speechCount = 0;

                    // 连续静音，停止说话
                    if (this.isSpeaking && this.silenceCount >= this.silenceThreshold) {
                        this.isSpeaking = false;
                        this.speechEndTime = Date.now();
                        this.waitingForFinalResult = true;
                        this.isWaitingResponse = true; // 停止说话后等待服务端响应

                        console.log('🔇 检测到停止说话，等待最终识别结果...');

                        // 通知Unity语音输入结束，等待处理
                        SendMessage('GameApp', 'OnSpeechEnd', '');

                        // 设置超时机制，如果长时间没有最终结果，强制结束会话
                        setTimeout(function() {
                            if (this.waitingForFinalResult) {
                                console.log('⏰ 等待最终结果超时，强制结束语音会话');
                                this.endSpeechSession('超时');
                            }
                        }.bind(this), 5000); // 5秒超时
                    }
                }

                return {
                    hasVoice: hasVoice,
                    isSpeaking: this.isSpeaking,
                    rms: rms
                };
            },

            // 检查发送频率限制
            checkSendRate: function() {
                var currentTime = Date.now();
                var currentSecond = Math.floor(currentTime / 1000);

                // 重置计数器（新的一秒）
                if (currentSecond !== this.lastSecond) {
                    this.sendCount = 0;
                    this.lastSecond = currentSecond;
                }

                // 检查是否超过频率限制
                if (this.sendCount >= this.maxSendRate) {
                    return false; // 超过限制
                }

                // 检查时间间隔
                if (currentTime - this.lastSendTime < this.sendInterval) {
                    return false; // 间隔太短
                }

                return true; // 可以发送
            },

            // 记录发送
            recordSend: function() {
                this.sendCount++;
                this.lastSendTime = Date.now();
            },

            // 结束语音会话
            endSpeechSession: function(reason) {
                console.log('🏁 结束语音会话，原因:', reason);

                this.speechSessionActive = false;
                this.waitingForFinalResult = false;
                this.isWaitingResponse = false;

                // 如果有识别到的文本，触发后续流程
                if (this.currentRecognitionText && this.currentRecognitionText.trim()) {
                    console.log('📝 最终识别文本:', this.currentRecognitionText);

                    // 通知Unity进行后续处理（UI显示、LLM处理、TTS等）
                    SendMessage('GameApp', 'OnSpeechRecognitionComplete', this.currentRecognitionText);
                } else {
                    console.log('❌ 没有识别到有效文本');
                    SendMessage('GameApp', 'OnSpeechRecognitionEmpty', '');
                }

                // 重置状态
                this.currentRecognitionText = '';
                this.speechEndTime = 0;
            }
        };
        
        window.unityVoiceRecognizer.init();
    },

    WebGLStartRecording: function(websocketUrlPtr) {
        var websocketUrl = UTF8ToString(websocketUrlPtr);
        var recognizer = window.unityVoiceRecognizer;
        
        if (recognizer.isRecording) {
            return;
        }

        // 重置ASR状态
        recognizer.asrStarted = false;
        
        // 连接WebSocket
        try {
            recognizer.websocket = new WebSocket(websocketUrl);
            
            recognizer.websocket.onopen = function() {
                console.log('WebSocket连接已建立，URL:', websocketUrl);
                console.log('连接状态:', recognizer.websocket.readyState);

                // 发送启动ASR命令
                var startCmd = {
                    action: 'start',
                    mode: 'ali'
                };
                recognizer.websocket.send(JSON.stringify(startCmd));
                console.log('已发送ASR启动命令:', JSON.stringify(startCmd));
            };
            
            // 定义可复用的事件处理函数
            recognizer.handleWebSocketMessage = function(event) {
                // 处理ASR服务端返回的消息
                try {
                    var data = event.data;
                    console.log('收到服务端消息:', data); // 添加调试信息

                    // 解析JSON格式的响应
                    if (typeof data === 'string') {
                        var jsonData = JSON.parse(data);
                        console.log('解析后的JSON数据:', jsonData); // 添加调试信息

                        // 检查多种可能的启动确认格式
                        if (jsonData.status === 'started' ||
                            jsonData.action === 'started' ||
                            (jsonData.header && jsonData.header.name === 'TranscriptionStarted')) {
                            console.log('ASR已启动，开始录音');
                            recognizer.asrStarted = true;
                        } else if (jsonData.type === 'result' ||
                                   (jsonData.header && jsonData.header.name === 'TranscriptionResultChanged')) {
                            // 实时识别结果
                            var resultText = jsonData.text || (jsonData.payload && jsonData.payload.result) || '';
                            console.log('💬 实时识别结果:', resultText);

                            if (resultText && recognizer.speechSessionActive) {
                                // 更新当前识别文本
                                recognizer.currentRecognitionText = resultText;

                                // 发送实时结果到Unity（用于UI实时显示）
                                SendMessage('GameApp', 'OnWebGLRecognitionResult', resultText);
                            }

                            // 如果还在说话，可以继续发送音频
                            if (recognizer.isSpeaking) {
                                recognizer.isWaitingResponse = false;
                            }

                        } else if (jsonData.type === 'final' ||
                                   (jsonData.header && jsonData.header.name === 'SentenceEnd') ||
                                   (jsonData.header && jsonData.header.name === 'TranscriptionCompleted')) {
                            // 最终识别结果
                            var finalText = jsonData.text || (jsonData.payload && jsonData.payload.result) || '';
                            console.log('✅ 最终识别结果:', finalText);

                            if (finalText) {
                                recognizer.currentRecognitionText = finalText;
                            }

                            // 如果正在等待最终结果，结束语音会话
                            if (recognizer.waitingForFinalResult) {
                                recognizer.endSpeechSession('收到最终结果');
                            } else {
                                // 不在等待状态，只是重置等待响应
                                recognizer.isWaitingResponse = false;
                            }
                        } else if (jsonData.header && jsonData.header.name === 'TaskFailed') {
                            // 处理任务失败
                            var errorMsg = jsonData.header.status_text || '未知错误';
                            console.error('ASR任务失败:', errorMsg);

                            if (errorMsg.includes('TOO_MANY_REQUESTS')) {
                                console.log('检测到限流错误，降低发送频率');
                                // 动态调整发送参数
                                recognizer.sendInterval = Math.min(recognizer.sendInterval * 1.5, 1000);
                                recognizer.maxSendRate = Math.max(recognizer.maxSendRate - 1, 2);
                                console.log('调整后参数 - 间隔:', recognizer.sendInterval + 'ms',
                                           '最大频率:', recognizer.maxSendRate + '/秒');

                                // 暂停发送一段时间
                                recognizer.isWaitingResponse = true;
                                setTimeout(function() {
                                    recognizer.isWaitingResponse = false;
                                    console.log('限流暂停结束，恢复发送');
                                }, 3000);
                            }
                        } else {
                            console.log('收到其他消息:', jsonData);
                            // 如果还没启动ASR，尝试强制启动
                            if (!recognizer.asrStarted) {
                                console.log('强制启动ASR音频发送');
                                recognizer.asrStarted = true;
                            }
                        }
                    } else {
                        console.warn('收到非字符串消息:', data);
                    }
                } catch (e) {
                    console.error('处理识别结果失败:', e, '原始数据:', event.data);
                }
            };

            recognizer.handleWebSocketError = function(error) {
                console.error('WebSocket错误:', error);
            };

            recognizer.handleWebSocketClose = function(event) {
                console.log('WebSocket连接已关闭, code:', event.code, 'reason:', event.reason);
                recognizer.asrStarted = false;
                recognizer.isWaitingResponse = false; // 重置等待状态

                // 分析关闭原因
                var closeReason = '';
                switch(event.code) {
                    case 1000: closeReason = '正常关闭'; break;
                    case 1001: closeReason = '端点离开'; break;
                    case 1002: closeReason = '协议错误'; break;
                    case 1003: closeReason = '不支持的数据'; break;
                    case 1005: closeReason = '服务端异常终止'; break;
                    case 1006: closeReason = '异常关闭'; break;
                    case 1011: closeReason = '服务器错误'; break;
                    default: closeReason = '未知错误(' + event.code + ')';
                }
                console.log('关闭原因:', closeReason);

                // 如果是异常关闭且还在录音中，尝试重连
                if (recognizer.isRecording && event.code !== 1000) {
                    // 对于1005错误（服务端异常），延长重连时间
                    var reconnectDelay = event.code === 1005 ? 5000 : 3000;
                    console.log('检测到异常关闭，' + (reconnectDelay/1000) + '秒后尝试重连...');

                    setTimeout(function() {
                        if (recognizer.isRecording) {
                            console.log('尝试重新连接WebSocket...');
                            recognizer.reconnectWebSocket(websocketUrl);
                        }
                    }, reconnectDelay);
                }
            };

            // 绑定事件处理函数
            recognizer.websocket.onmessage = recognizer.handleWebSocketMessage;
            recognizer.websocket.onerror = recognizer.handleWebSocketError;
            recognizer.websocket.onclose = recognizer.handleWebSocketClose;
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            return;
        }
        
        // 请求麦克风权限并开始录音
        navigator.mediaDevices.getUserMedia({
            audio: {
                sampleRate: 16000,
                channelCount: 1,
                echoCancellation: true,
                noiseSuppression: true
            }
        })
        .then(function(stream) {
            recognizer.isRecording = true;

            // 使用AudioContext进行实时音频处理
            var audioContext = recognizer.audioContext;
            var source = audioContext.createMediaStreamSource(stream);
            var processor = audioContext.createScriptProcessor(1024, 1, 1);

            // 音频数据处理
            processor.onaudioprocess = function(event) {
                if (!recognizer.isRecording) {
                    return;
                }

                if (!recognizer.websocket) {
                    console.log('WebSocket不存在，尝试重连...');
                    recognizer.reconnectWebSocket(websocketUrl);
                    return;
                }

                if (recognizer.websocket.readyState !== WebSocket.OPEN) {
                    console.log('WebSocket未连接 (状态: ' + recognizer.websocket.readyState + ')，跳过音频发送');
                    if (recognizer.websocket.readyState === WebSocket.CLOSED) {
                        console.log('检测到连接已关闭，尝试重连...');
                        recognizer.reconnectWebSocket(websocketUrl);
                    }
                    return;
                }

                // 确保ASR已启动后再发送音频数据
                if (!recognizer.asrStarted) {
                    return;
                }

                var inputBuffer = event.inputBuffer;
                var inputData = inputBuffer.getChannelData(0);

                // 语音活动检测
                var vadResult = recognizer.detectVoiceActivity(inputData);

                // 流控制：检查是否应该发送数据
                var shouldSend = false;

                if (vadResult.isSpeaking && !recognizer.isWaitingResponse) {
                    // 正在说话且不在等待响应状态，检查发送频率
                    if (recognizer.checkSendRate()) {
                        shouldSend = true;
                    }
                } else if (recognizer.isWaitingResponse) {
                    // 等待服务端响应，暂停发送
                    return;
                }

                if (!shouldSend) {
                    return;
                }

                // 将Float32Array转换为16位PCM数据
                var pcmData = new Int16Array(inputData.length);
                for (var i = 0; i < inputData.length; i++) {
                    var sample = Math.max(-1, Math.min(1, inputData[i]));
                    pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
                }

                // 发送PCM数据
                try {
                    // 检查连接状态
                    if (recognizer.websocket.readyState !== WebSocket.OPEN) {
                        console.log('发送前检测到连接异常，状态:', recognizer.websocket.readyState);
                        return;
                    }

                    recognizer.websocket.send(pcmData.buffer);
                    recognizer.recordSend(); // 记录发送

                    console.log('发送PCM数据 - 长度:', pcmData.length,
                               'RMS:', vadResult.rms.toFixed(4),
                               '说话状态:', vadResult.isSpeaking,
                               '等待响应:', recognizer.isWaitingResponse,
                               '发送计数:', recognizer.sendCount + '/' + recognizer.maxSendRate);

                } catch (e) {
                    console.error('发送PCM数据失败:', e, '连接状态:', recognizer.websocket.readyState);

                    // 发送失败时暂停一段时间，避免频繁重试
                    recognizer.isWaitingResponse = true;
                    setTimeout(function() {
                        recognizer.isWaitingResponse = false;
                    }, 1000);

                    if (recognizer.websocket.readyState === WebSocket.CLOSED) {
                        console.log('检测到发送时连接已关闭，尝试重连...');
                        recognizer.reconnectWebSocket(websocketUrl);
                    }
                }
            };

            // 连接音频节点
            source.connect(processor);
            processor.connect(audioContext.destination);

            // 保存引用以便清理
            recognizer.audioSource = source;
            recognizer.audioProcessor = processor;
            recognizer.mediaStream = stream;

            // 定期检查连接状态和健康度
            recognizer.connectionCheckInterval = setInterval(function() {
                if (recognizer.websocket && recognizer.isRecording) {
                    console.log('连接状态检查 - readyState:', recognizer.websocket.readyState,
                               'ASR状态:', recognizer.asrStarted,
                               '说话状态:', recognizer.isSpeaking,
                               '等待响应:', recognizer.isWaitingResponse);

                    if (recognizer.websocket.readyState === WebSocket.CLOSED) {
                        console.log('定期检查发现连接已关闭，尝试重连...');
                        recognizer.reconnectWebSocket(websocketUrl);
                    } else if (recognizer.websocket.readyState === WebSocket.CONNECTING) {
                        console.log('连接正在建立中...');
                    }

                    // 检查是否长时间等待响应（可能服务端卡住了）
                    if (recognizer.isWaitingResponse && recognizer.lastSendTime > 0) {
                        var waitTime = Date.now() - recognizer.lastSendTime;
                        if (waitTime > 10000) { // 等待超过10秒
                            console.log('等待服务端响应超时，重置状态');
                            recognizer.isWaitingResponse = false;
                        }
                    }
                }
            }, 3000); // 每3秒检查一次

        })
        .catch(function(error) {
            console.error('获取麦克风权限失败:', error);
            recognizer.isRecording = false;
        });
    },

    WebGLStopRecording: function() {
        var recognizer = window.unityVoiceRecognizer;

        if (!recognizer.isRecording) {
            return;
        }

        recognizer.isRecording = false;
        recognizer.asrStarted = false;

        // 重置语音检测状态
        recognizer.isSpeaking = false;
        recognizer.silenceCount = 0;
        recognizer.speechCount = 0;
        recognizer.isWaitingResponse = false;

        // 重置语音会话状态
        if (recognizer.speechSessionActive) {
            recognizer.endSpeechSession('手动停止录音');
        }

        // 发送停止ASR命令
        if (recognizer.websocket && recognizer.websocket.readyState === WebSocket.OPEN) {
            var stopCmd = {action: 'stop'};
            recognizer.websocket.send(JSON.stringify(stopCmd));
            console.log('已发送ASR停止命令');
        }

        // 断开音频节点连接
        if (recognizer.audioProcessor) {
            recognizer.audioProcessor.disconnect();
            recognizer.audioProcessor = null;
        }

        if (recognizer.audioSource) {
            recognizer.audioSource.disconnect();
            recognizer.audioSource = null;
        }

        // 停止媒体流
        if (recognizer.mediaStream) {
            recognizer.mediaStream.getTracks().forEach(function(track) {
                track.stop();
            });
            recognizer.mediaStream = null;
        }

        // 关闭WebSocket连接
        if (recognizer.websocket && recognizer.websocket.readyState === WebSocket.OPEN) {
            recognizer.websocket.close();
        }

        recognizer.websocket = null;
    }
});
