mergeInto(LibraryManager.library, {
    CopyToClipboard: function(textPtr) {
        var text = UTF8ToString(textPtr);
        
        // 使用现代浏览器的Clipboard API
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(text).then(function() {
                console.log('文本已复制到剪贴板: ' + text);
            }).catch(function(err) {
                console.error('复制到剪贴板失败: ', err);
                // 回退到传统方法
                fallbackCopyTextToClipboard(text);
            });
        } else {
            // 回退到传统方法
            fallbackCopyTextToClipboard(text);
        }
        
        function fallbackCopyTextToClipboard(text) {
            var textArea = document.createElement("textarea");
            textArea.value = text;
            
            // 避免滚动到底部
            textArea.style.top = "0";
            textArea.style.left = "0";
            textArea.style.position = "fixed";
            textArea.style.opacity = "0";
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    console.log('文本已复制到剪贴板(传统方法): ' + text);
                } else {
                    console.error('复制到剪贴板失败(传统方法)');
                }
            } catch (err) {
                console.error('复制到剪贴板出错: ', err);
            }
            
            document.body.removeChild(textArea);
        }
    }
});
