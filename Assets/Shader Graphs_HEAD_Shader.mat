%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Shader Graphs_HEAD_Shader
  m_Shader: {fileID: -6465566751694194690, guid: 426aa4334970fd8438e507b517fa8c42, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - _RECEIVE_SHADOWS_OFF
  m_InvalidKeywords:
  - _RECEIVESHADOWS
  - _RECEIVE_SHADOWS_ON
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap:
    RenderType: Opaque
  disabledShaderPasses:
  - MOTIONVECTORS
  - SHADOWCASTER
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseColor:
        m_Texture: {fileID: 2800000, guid: d144608c9aa41d1488087a06755460c9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: bbf349b2d69a6014c9ec1383dfbfd740, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Occ:
        m_Texture: {fileID: 2800000, guid: d9dee3c18b946ad46b310b63158af942, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Sm:
        m_Texture: {fileID: 2800000, guid: f4bda99462f9a264ebcb1bdae75aeb4e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Smoothness:
        m_Texture: {fileID: 2800000, guid: 87b9c61713eebdb4a9be9ea1a209ebd3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture2D:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _id:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaClip: 0
    - _AlphaToMask: 0
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _CastShadows: 0
    - _Cull: 2
    - _DstBlend: 0
    - _Float: 0
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RECEIVESHADOWS: 1
    - _RECEIVE_SHADOWS_OFF: 0
    - _ReceiveShadows: 0
    - _Smoothness: 0.59
    - _SmoothnessValue: 0.676
    - _SrcBlend: 1
    - _Surface: 0
    - _WorkflowMode: 1
    - _ZTest: 4
    - _ZWrite: 1
    - _ZWriteControl: 0
    m_Colors:
    - _Em: {r: 0.09433961, g: 0.09433961, b: 0.09433961, a: 1}
  m_BuildTextureStacks: []
--- !u!114 &7543412459146299248
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
