trackSelection = true
refreshSearchWindowsInPlayMode = false
fetchPreview = true
defaultFlags = 0
keepOpen = false
queryFolder = "Assets"
onBoardingDoNotAskAgain = true
showPackageIndexes = false
showStatusBar = false
scopes = {
	"picker_window_position_offset.0B486DF2" = "-659;108;320;550"
	"picker_search_text.0B486DF2" = ""
	"last_search.0B486DF2" = ""
	"OpenInspectorPreview.0B486DF2" = "0"
	"currentGroup.0B486DF2" = null
}
providers = {
	asset = {
		active = true
		priority = 25
		defaultAction = null
	}
	profilermarkers = {
		active = false
		priority = 100
		defaultAction = null
	}
	adb = {
		active = false
		priority = 2500
		defaultAction = null
	}
	store = {
		active = true
		priority = 100
		defaultAction = null
	}
	find = {
		active = true
		priority = 25
		defaultAction = null
	}
	log = {
		active = false
		priority = 210
		defaultAction = null
	}
	performance = {
		active = false
		priority = 100
		defaultAction = null
	}
	scene = {
		active = true
		priority = 50
		defaultAction = null
	}
	packages = {
		active = true
		priority = 90
		defaultAction = null
	}
	presets_provider = {
		active = false
		priority = -10
		defaultAction = null
	}
}
objectSelectors = {
}
recentSearches = [
]
searchItemFavorites = [
]
savedSearchesSortOrder = 0
showSavedSearchPanel = false
hideTabs = false
expandedQueries = [
]
queryBuilder = false
ignoredProperties = "id;name;classname;imagecontentshash"
helperWidgetCurrentArea = "all"
disabledIndexers = ""
minIndexVariations = 2
findProviderIndexHelper = true